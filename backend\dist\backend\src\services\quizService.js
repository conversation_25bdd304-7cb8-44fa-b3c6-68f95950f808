"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.quizService = exports.QuizService = void 0;
const supabaseService_1 = require("./supabaseService");
class QuizService {
    async getQuestionsByStudySet(studySetId, userId) {
        // Verify user owns the study set
        const { data: studySet, error: studySetError } = await supabaseService_1.supabase
            .from('study_sets')
            .select('id')
            .eq('id', studySetId)
            .eq('user_id', userId)
            .single();
        if (studySetError || !studySet) {
            throw new Error('Study set not found or access denied');
        }
        const { data, error } = await supabaseService_1.supabase
            .from('quiz_questions')
            .select('*')
            .eq('study_set_id', studySetId)
            .order('created_at', { ascending: true });
        if (error) {
            throw new Error(`Failed to get quiz questions: ${error.message}`);
        }
        return data || [];
    }
    async createQuizQuestion(studySetId, userId, questionData) {
        // Verify user owns the study set
        const { data: studySet, error: studySetError } = await supabaseService_1.supabase
            .from('study_sets')
            .select('id')
            .eq('id', studySetId)
            .eq('user_id', userId)
            .single();
        if (studySetError || !studySet) {
            throw new Error('Study set not found or access denied');
        }
        const { data, error } = await supabaseService_1.supabase
            .from('quiz_questions')
            .insert({
            study_set_id: studySetId,
            question_text: questionData.question_text,
            question_type: questionData.question_type,
            options: questionData.options,
            correct_answers: questionData.correct_answers,
            explanation: questionData.explanation,
            difficulty_level: questionData.difficulty_level || 3,
            is_ai_generated: questionData.is_ai_generated || false
        })
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to create quiz question: ${error.message}`);
        }
        return data;
    }
    async updateQuizQuestion(questionId, userId, updates) {
        // Verify user owns the question through study set
        const { data: question, error: questionError } = await supabaseService_1.supabase
            .from('quiz_questions')
            .select('study_set_id')
            .eq('id', questionId)
            .single();
        if (questionError || !question) {
            throw new Error('Quiz question not found');
        }
        const { data: studySet, error: studySetError } = await supabaseService_1.supabase
            .from('study_sets')
            .select('id')
            .eq('id', question.study_set_id)
            .eq('user_id', userId)
            .single();
        if (studySetError || !studySet) {
            throw new Error('Access denied');
        }
        const { data, error } = await supabaseService_1.supabase
            .from('quiz_questions')
            .update(updates)
            .eq('id', questionId)
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to update quiz question: ${error.message}`);
        }
        return data;
    }
    async deleteQuizQuestion(questionId, userId) {
        // Verify user owns the question through study set
        const { data: question, error: questionError } = await supabaseService_1.supabase
            .from('quiz_questions')
            .select('study_set_id')
            .eq('id', questionId)
            .single();
        if (questionError || !question) {
            throw new Error('Quiz question not found');
        }
        const { data: studySet, error: studySetError } = await supabaseService_1.supabase
            .from('study_sets')
            .select('id')
            .eq('id', question.study_set_id)
            .eq('user_id', userId)
            .single();
        if (studySetError || !studySet) {
            throw new Error('Access denied');
        }
        const { error } = await supabaseService_1.supabase
            .from('quiz_questions')
            .delete()
            .eq('id', questionId);
        if (error) {
            throw new Error(`Failed to delete quiz question: ${error.message}`);
        }
    }
    async recordQuizAttempt(questionId, _userId, isCorrect) {
        // First get current attempt counts
        const { data: current, error: getCurrentError } = await supabaseService_1.supabase
            .from('quiz_questions')
            .select('times_answered, times_correct')
            .eq('id', questionId)
            .single();
        if (getCurrentError || !current) {
            throw new Error('Quiz question not found');
        }
        const updateData = {
            times_answered: current.times_answered + 1,
            times_correct: isCorrect ? current.times_correct + 1 : current.times_correct
        };
        const { data, error } = await supabaseService_1.supabase
            .from('quiz_questions')
            .update(updateData)
            .eq('id', questionId)
            .select()
            .single();
        if (error) {
            throw new Error(`Failed to record quiz attempt: ${error.message}`);
        }
        return data;
    }
    async getQuizStatistics(studySetId, userId) {
        // Verify user owns the study set
        const { data: studySet, error: studySetError } = await supabaseService_1.supabase
            .from('study_sets')
            .select('id')
            .eq('id', studySetId)
            .eq('user_id', userId)
            .single();
        if (studySetError || !studySet) {
            throw new Error('Study set not found or access denied');
        }
        const { data: questions, error } = await supabaseService_1.supabase
            .from('quiz_questions')
            .select('id, question_text, times_attempted, times_correct')
            .eq('study_set_id', studySetId);
        if (error) {
            throw new Error(`Failed to get quiz statistics: ${error.message}`);
        }
        const totalQuestions = questions?.length || 0;
        const totalAttempts = questions?.reduce((sum, q) => sum + q.times_attempted, 0) || 0;
        const correctAnswers = questions?.reduce((sum, q) => sum + q.times_correct, 0) || 0;
        const averageScore = totalAttempts > 0 ? (correctAnswers / totalAttempts) * 100 : 0;
        const questionStats = questions?.map(q => ({
            id: q.id,
            question_text: q.question_text,
            times_attempted: q.times_attempted,
            times_correct: q.times_correct,
            success_rate: q.times_attempted > 0 ? (q.times_correct / q.times_attempted) * 100 : 0
        })) || [];
        return {
            totalQuestions,
            totalAttempts,
            correctAnswers,
            averageScore,
            questionStats
        };
    }
}
exports.QuizService = QuizService;
exports.quizService = new QuizService();
//# sourceMappingURL=quizService.js.map