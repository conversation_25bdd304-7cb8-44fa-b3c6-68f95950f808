import { create } from "zustand";
import { UserProfile } from "../../../shared/types";
import { authService } from "../services/auth";

interface AuthState {
  user: UserProfile | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (
    email: string,
    password: string
  ) => Promise<{ success: boolean; error?: string }>;
  signup: (
    email: string,
    password: string,
    name?: string
  ) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<void>;
  updateUser: (updates: Partial<UserProfile>) => void;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  isLoading: false,
  isAuthenticated: false,

  login: async (email, password) => {
    set({ isLoading: true });
    try {
      const result = await authService.signIn(email, password);
      if (result.success && result.user) {
        set({ user: result.user, isAuthenticated: true, isLoading: false });
        return { success: true };
      }
      set({ isLoading: false });
      return { success: false, error: result.error || "Login failed" };
    } catch {
      set({ isLoading: false });
      return { success: false, error: "Network error" };
    }
  },

  signup: async (email, password, name) => {
    set({ isLoading: true });
    try {
      const result = await authService.signUp(email, password, name);
      if (result.success && result.user) {
        set({ user: result.user, isAuthenticated: true, isLoading: false });
        return { success: true };
      }
      set({ isLoading: false });
      return { success: false, error: result.error || "Signup failed" };
    } catch {
      set({ isLoading: false });
      return { success: false, error: "Network error" };
    }
  },

  logout: async () => {
    set({ isLoading: true });
    try {
      await authService.signOut();
    } finally {
      set({ user: null, isAuthenticated: false, isLoading: false });
    }
  },

  checkAuth: async () => {
    set({ isLoading: true });
    try {
      const user = await authService.getCurrentUser();
      set({ user, isAuthenticated: !!user, isLoading: false });
    } catch {
      set({ user: null, isAuthenticated: false, isLoading: false });
    }
  },

  updateUser: (updates) => {
    const { user } = get();
    if (user) set({ user: { ...user, ...updates } });
  },
}));
