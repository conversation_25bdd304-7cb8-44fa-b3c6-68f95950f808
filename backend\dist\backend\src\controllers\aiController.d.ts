import { Request, Response } from 'express';
export declare const generateFlashcards: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const generateQuiz: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
export declare const testAIConnection: (_req: Request, res: Response) => Promise<void>;
export declare const getStudySets: (req: Request, res: Response) => Promise<void>;
export declare const getStudySetContent: (req: Request, res: Response) => Promise<Response<any, Record<string, any>> | undefined>;
//# sourceMappingURL=aiController.d.ts.map