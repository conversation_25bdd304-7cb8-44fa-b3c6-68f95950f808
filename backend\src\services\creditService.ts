import { supabase } from './supabaseService';
import { CreditTransaction, AIOperationCost } from '../../../shared/types';

export class CreditService {
  // Get current AI operation cost
  async getOperationCost(operationType: string): Promise<number> {
    const { data, error } = await supabase
      .from('ai_operation_costs')
      .select('credits_required')
      .eq('operation_type', operationType)
      .eq('is_active', true)
      .single();

    if (error || !data) {
      throw new Error(`Invalid operation type: ${operationType}`);
    }

    return data.credits_required;
  }

  // Get all active operation costs
  async getAllOperationCosts(): Promise<AIOperationCost[]> {
    const { data, error } = await supabase
      .from('ai_operation_costs')
      .select('*')
      .eq('is_active', true)
      .order('operation_type');

    if (error) {
      throw new Error(`Failed to get operation costs: ${error.message}`);
    }

    return data || [];
  }

  // Atomic credit deduction using stored procedure
  async deductCredits(
    userId: string,
    operationType: string,
    metadata?: any,
    studySetId?: string
  ): Promise<{ success: boolean; remainingCredits: number; message: string }> {
    const cost = await this.getOperationCost(operationType);

    const { data, error } = await supabase.rpc('deduct_credits', {
      p_user_id: userId,
      p_credits_to_deduct: cost,
      p_operation_type: operationType,
      p_description: `AI generation: ${operationType}`,
      p_metadata: metadata || {},
      p_study_set_id: studySetId || null
    });

    if (error) {
      throw new CreditError('Credit deduction failed', error);
    }

    return data[0]; // Returns {success, remaining_credits, message}
  }

  // Add credits for purchases/subscriptions
  async addCredits(
    userId: string,
    creditsToAdd: number,
    source: string,
    referenceId?: string
  ): Promise<{ success: boolean; newBalance: number; message: string }> {
    const { data, error } = await supabase.rpc('add_credits', {
      p_user_id: userId,
      p_credits_to_add: creditsToAdd,
      p_source: source,
      p_reference_id: referenceId || null
    });

    if (error) {
      throw new CreditError('Credit addition failed', error);
    }

    return data[0];
  }

  // Get user's current credit balance
  async getUserCredits(userId: string): Promise<number> {
    const { data, error } = await supabase
      .from('users')
      .select('credits_remaining')
      .eq('id', userId)
      .single();

    if (error || !data) {
      throw new Error('User not found');
    }

    return data.credits_remaining;
  }

  // Validate credit operation before execution
  async validateCreditOperation(userId: string, operationType: string): Promise<boolean> {
    const [userCredits, requiredCredits] = await Promise.all([
      this.getUserCredits(userId),
      this.getOperationCost(operationType)
    ]);

    return userCredits >= requiredCredits;
  }

  // Get user's credit transaction history
  async getCreditHistory(
    userId: string,
    limit = 50,
    offset = 0
  ): Promise<{ transactions: CreditTransaction[]; total: number }> {
    // Get transactions
    const { data: transactions, error: transError } = await supabase
      .from('credit_transactions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (transError) {
      throw new Error(`Failed to get credit history: ${transError.message}`);
    }

    // Get total count
    const { count, error: countError } = await supabase
      .from('credit_transactions')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    if (countError) {
      throw new Error(`Failed to get credit history count: ${countError.message}`);
    }

    return {
      transactions: transactions || [],
      total: count || 0
    };
  }

  // Get credit usage statistics
  async getCreditStats(userId: string, days = 30): Promise<{
    totalUsed: number;
    totalAdded: number;
    operationBreakdown: { operation_type: string; credits_used: number; count: number }[];
  }> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const { data, error } = await supabase
      .from('credit_transactions')
      .select('credits_used, operation_type')
      .eq('user_id', userId)
      .gte('created_at', startDate.toISOString());

    if (error) {
      throw new Error(`Failed to get credit stats: ${error.message}`);
    }

    const transactions = data || [];
    
    const totalUsed = transactions
      .filter(t => t.credits_used > 0)
      .reduce((sum, t) => sum + t.credits_used, 0);
    
    const totalAdded = Math.abs(transactions
      .filter(t => t.credits_used < 0)
      .reduce((sum, t) => sum + t.credits_used, 0));

    // Group by operation type
    const operationMap = new Map<string, { credits_used: number; count: number }>();
    
    transactions
      .filter(t => t.credits_used > 0)
      .forEach(t => {
        const existing = operationMap.get(t.operation_type) || { credits_used: 0, count: 0 };
        operationMap.set(t.operation_type, {
          credits_used: existing.credits_used + t.credits_used,
          count: existing.count + 1
        });
      });

    const operationBreakdown = Array.from(operationMap.entries()).map(([operation_type, stats]) => ({
      operation_type,
      ...stats
    }));

    return {
      totalUsed,
      totalAdded,
      operationBreakdown
    };
  }

  // Check if user has sufficient credits for operation
  async checkSufficientCredits(userId: string, operationType: string): Promise<{
    sufficient: boolean;
    currentCredits: number;
    requiredCredits: number;
    shortfall: number;
  }> {
    const [currentCredits, requiredCredits] = await Promise.all([
      this.getUserCredits(userId),
      this.getOperationCost(operationType)
    ]);

    const sufficient = currentCredits >= requiredCredits;
    const shortfall = sufficient ? 0 : requiredCredits - currentCredits;

    return {
      sufficient,
      currentCredits,
      requiredCredits,
      shortfall
    };
  }
}

// Custom error class for credit operations
export class CreditError extends Error {
  constructor(message: string, public originalError?: any) {
    super(message);
    this.name = 'CreditError';
  }
}

export const creditService = new CreditService();
