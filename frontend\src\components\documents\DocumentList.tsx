import React, { useEffect, useState } from 'react';
import { DocumentMetadata } from '../../../../shared/types';
import { useDocumentStore } from '../../stores/documentStore';
import { DocumentCard } from './DocumentCard';
import { Input } from '../common/Input';
import { Button } from '../common/Button';

export const DocumentList: React.FC = () => {
  const {
    documents,
    selectedDocuments,
    isLoading,
    fetchDocuments,
    searchDocuments,
    clearSelection,
    selectAll,
    deleteDocument
  } = useDocumentStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<DocumentMetadata[] | null>(null);
  const [isSearching, setIsSearching] = useState(false);

  useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  const handleSearch = async () => {
    if (searchQuery.trim().length < 2) {
      setSearchResults(null);
      return;
    }

    setIsSearching(true);
    try {
      const results = await searchDocuments(searchQuery.trim());
      setSearchResults(results);
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const clearSearch = () => {
    setSearchQuery('');
    setSearchResults(null);
  };

  const handleBulkDelete = async () => {
    if (selectedDocuments.size === 0) return;

    const confirmDelete = window.confirm(
      `Are you sure you want to delete ${selectedDocuments.size} document(s)? This action cannot be undone.`
    );

    if (!confirmDelete) return;

    try {
      const deletePromises = Array.from(selectedDocuments).map(id => deleteDocument(id));
      await Promise.all(deletePromises);
      clearSelection();
    } catch (error) {
      console.error('Bulk delete error:', error);
      alert('Some documents could not be deleted. Please try again.');
    }
  };

  const displayDocuments = searchResults || documents;
  const hasSelection = selectedDocuments.size > 0;

  if (isLoading && documents.length === 0) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-gray-400">Loading documents...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Actions */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="flex gap-2">
            <Input
              placeholder="Search documents..."
              value={searchQuery}
              onChange={setSearchQuery}
            />
            <Button
              onClick={handleSearch}
              isLoading={isSearching}
              disabled={searchQuery.trim().length < 2}
            >
              Search
            </Button>
            {searchResults && (
              <Button
                onClick={clearSearch}
                variant="secondary"
              >
                Clear
              </Button>
            )}
          </div>
        </div>

        {/* Bulk Actions */}
        {hasSelection && (
          <div className="flex gap-2">
            <Button
              onClick={selectAll}
              variant="secondary"
              size="sm"
            >
              Select All
            </Button>
            <Button
              onClick={clearSelection}
              variant="secondary"
              size="sm"
            >
              Clear ({selectedDocuments.size})
            </Button>
            <Button
              onClick={handleBulkDelete}
              variant="danger"
              size="sm"
            >
              Delete Selected
            </Button>
          </div>
        )}
      </div>

      {/* Search Results Info */}
      {searchResults && (
        <div className="text-sm text-gray-400">
          Found {searchResults.length} document(s) matching "{searchQuery}"
        </div>
      )}

      {/* Document Grid */}
      {displayDocuments.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            {searchResults ? 'No documents found matching your search.' : 'No documents uploaded yet.'}
          </div>
          {!searchResults && (
            <p className="text-sm text-gray-500">
              Upload your first document to get started with AI-powered study materials.
            </p>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {displayDocuments.map((document) => (
            <DocumentCard
              key={document.id}
              document={document}
            />
          ))}
        </div>
      )}
    </div>
  );
};
