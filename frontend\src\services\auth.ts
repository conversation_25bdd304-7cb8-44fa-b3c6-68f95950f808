import { createClient } from "@supabase/supabase-js";
import { UserProfile, AuthResult } from "../../../shared/types";

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

class AuthService {
  async signUp(
    email: string,
    password: string,
    name?: string
  ): Promise<AuthResult> {
    try {
      const response = await fetch("/api/auth/signup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password, name }),
      });
      const result = await response.json();
      return result;
    } catch {
      return { success: false, error: "Network error during signup" };
    }
  }

  async signIn(email: string, password: string): Promise<AuthResult> {
    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      });
      const result = await response.json();
      if (result.success && result.token) {
        localStorage.setItem("auth_token", result.token);
      }
      return result;
    } catch {
      return { success: false, error: "Network error during login" };
    }
  }

  async signOut(): Promise<void> {
    try {
      const token = localStorage.getItem("auth_token");
      if (token) {
        await fetch("/api/auth/logout", {
          method: "POST",
          headers: { Authorization: `Bearer ${token}` },
        });
      }
    } finally {
      localStorage.removeItem("auth_token");
    }
  }

  async getCurrentUser(): Promise<UserProfile | null> {
    try {
      const token = localStorage.getItem("auth_token");
      if (!token) return null;
      const response = await fetch("/api/auth/user", {
        headers: { Authorization: `Bearer ${token}` },
      });
      if (!response.ok) {
        if (response.status === 401) localStorage.removeItem("auth_token");
        return null;
      }
      const result = await response.json();
      return result.success ? result.data : null;
    } catch {
      return null;
    }
  }

  getToken(): string | null {
    return localStorage.getItem("auth_token");
  }

  isAuthenticated(): boolean {
    return !!this.getToken();
  }
}

export const authService = new AuthService();
