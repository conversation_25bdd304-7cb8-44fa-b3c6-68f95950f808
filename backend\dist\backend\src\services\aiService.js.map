{"version": 3, "file": "aiService.js", "sourceRoot": "", "sources": ["../../../../src/services/aiService.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAG1B,MAAa,SAAS;IAKpB;QAHiB,YAAO,GAAG,8BAA8B,CAAC;QACzC,UAAK,GAAG,yBAAyB,CAAC;QAGjD,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,kBAAmB,CAAC;QAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;QACzE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CACtB,OAAe,EACf,QAAgB,EAAE,EAClB,YAAqB;QAErB,MAAM,MAAM,GAAG,IAAI,CAAC,oBAAoB,CAAC,OAAO,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;QAEvE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACnD,OAAO,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,OAAO,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CACzB,OAAe,EACf,QAAgB,EAAE,EAClB,YAAqB;QAErB,MAAM,MAAM,GAAG,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC;QAElE,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;YACnD,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAC1C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,OAAO,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAEO,oBAAoB,CAAC,OAAe,EAAE,KAAa,EAAE,YAAqB;QAChF,MAAM,UAAU,GAAG;SACd,KAAK;;;;;;;;;;EAUZ,YAAY,CAAC,CAAC,CAAC,4BAA4B,YAAY,IAAI,CAAC,CAAC,CAAC,EAAE;;;EAGhE,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;+DAWd,CAAC;QAE5D,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,eAAe,CAAC,OAAe,EAAE,KAAa,EAAE,YAAqB;QAC3E,MAAM,UAAU,GAAG;SACd,KAAK;;;;;;;;;;EAUZ,YAAY,CAAC,CAAC,CAAC,4BAA4B,YAAY,IAAI,CAAC,CAAC,CAAC,EAAE;;;EAGhE,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE;;;;;;;;;;;;;;+DAcd,CAAC;QAE5D,OAAO,UAAU,CAAC;IACpB,CAAC;IAEO,KAAK,CAAC,cAAc,CAAC,MAAc;QACzC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,eAAK,CAAC,IAAI,CAC/B,GAAG,IAAI,CAAC,OAAO,mBAAmB,EAClC;gBACE,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,MAAM;qBAChB;iBACF;gBACD,WAAW,EAAE,GAAG;gBAChB,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,GAAG;aACX,EACD;gBACE,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,MAAM,EAAE;oBACxC,cAAc,EAAE,kBAAkB;oBAClC,cAAc,EAAE,qBAAqB;oBACrC,SAAS,EAAE,kCAAkC;iBAC9C;gBACD,OAAO,EAAE,KAAK,CAAC,oBAAoB;aACpC,CACF,CAAC;YAEF,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC;gBACnD,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;YACtD,CAAC;YAED,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;oBACnC,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;gBAC7E,CAAC;qBAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;oBAC1C,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;gBACtD,CAAC;qBAAM,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;oBACzC,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;gBAClD,CAAC;YACH,CAAC;YACD,MAAM,IAAI,KAAK,CAAC,qBAAqB,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QACnG,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,QAAgB;QAC7C,IAAI,CAAC;YACH,kDAAkD;YAClD,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YAExE,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAE7C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC/B,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,CAAC;YAED,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;gBACpC,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;oBAC9B,MAAM,IAAI,KAAK,CAAC,8BAA8B,KAAK,yBAAyB,CAAC,CAAC;gBAChF,CAAC;gBAED,OAAO;oBACL,KAAK,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE;oBAChC,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE;oBAC9B,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,gBAAgB,CAAC;oBACrE,eAAe,EAAE,IAAI;oBACrB,YAAY,EAAE,CAAC;oBACf,gBAAgB,EAAE,SAAS;iBAC5B,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,qCAAqC,EAAE,QAAQ,CAAC,CAAC;YAC/D,MAAM,IAAI,KAAK,CAAC,gCAAgC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QAC9G,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,QAAgB;QACxC,IAAI,CAAC;YACH,kDAAkD;YAClD,MAAM,aAAa,GAAG,QAAQ,CAAC,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC;YAExE,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAE5C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,CAAC;YAED,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;gBACvC,IAAI,CAAC,QAAQ,CAAC,aAAa,IAAI,CAAC,QAAQ,CAAC,aAAa,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;oBACpF,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,2BAA2B,CAAC,CAAC;gBACjF,CAAC;gBAED,MAAM,UAAU,GAAG,CAAC,iBAAiB,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC;gBACnF,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;oBACjD,MAAM,IAAI,KAAK,CAAC,kCAAkC,KAAK,KAAK,QAAQ,CAAC,aAAa,EAAE,CAAC,CAAC;gBACxF,CAAC;gBAED,OAAO;oBACL,aAAa,EAAE,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,IAAI,EAAE;oBACpD,aAAa,EAAE,QAAQ,CAAC,aAAa;oBACrC,OAAO,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS;oBAC9F,eAAe,EAAE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC;wBACtD,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;wBAChE,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,IAAI,EAAE,CAAC;oBAC7C,WAAW,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS;oBACnF,gBAAgB,EAAE,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,gBAAgB,CAAC;oBACzE,eAAe,EAAE,IAAI;oBACrB,cAAc,EAAE,CAAC;oBACjB,aAAa,EAAE,CAAC;iBACjB,CAAC;YACJ,CAAC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,QAAQ,CAAC,CAAC;YAC1D,MAAM,IAAI,KAAK,CAAC,gCAAgC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QAC9G,CAAC;IACH,CAAC;IAEO,uBAAuB,CAAC,KAAU;QACxC,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,QAAQ,GAAG,CAAC,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACpD,OAAO,CAAC,CAAC,CAAC,+BAA+B;QAC3C,CAAC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC9B,CAAC;IAED,+BAA+B;IAC/B,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,iDAAiD,CAAC,CAAC;YAC9F,OAAO,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;YAC3D,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;CACF;AAnPD,8BAmPC;AAED,MAAa,OAAQ,SAAQ,KAAK;IAChC,YAAY,OAAe,EAAS,aAAmB;QACrD,KAAK,CAAC,OAAO,CAAC,CAAC;QADmB,kBAAa,GAAb,aAAa,CAAM;QAErD,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;IACxB,CAAC;CACF;AALD,0BAKC;AAEY,QAAA,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC"}