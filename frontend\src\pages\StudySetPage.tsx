import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useStudyStore } from '../stores/studyStore';
import { Button } from '../components/common/Button';

export const StudySetPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { studySetContent, isLoading, error, fetchStudySetContent } = useStudyStore();
  const [selectedMode, setSelectedMode] = useState<'flashcards' | 'quiz' | null>(null);

  useEffect(() => {
    if (id) {
      fetchStudySetContent(id).catch(console.error);
    }
  }, [id, fetchStudySetContent]);

  const handleStartStudy = async () => {
    if (!id || !selectedMode) return;

    try {
      await useStudyStore.getState().startStudySession(id, selectedMode);
      navigate(`/study/${id}/${selectedMode}`);
    } catch (error: any) {
      alert(error.message || 'Failed to start study session');
    }
  };

  if (isLoading) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
          <span className="ml-3 text-gray-400">Loading study set...</span>
        </div>
      </div>
    );
  }

  if (error || !studySetContent?.studySet) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <div className="text-red-400 mb-4">
            {error || 'Study set not found'}
          </div>
          <Button onClick={() => navigate('/dashboard')} variant="secondary">
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  const { studySet, flashcards, questions } = studySetContent;
  const hasFlashcards = flashcards && flashcards.length > 0;
  const hasQuestions = questions && questions.length > 0;

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <button
          onClick={() => navigate('/dashboard')}
          className="text-gray-400 hover:text-white mb-4 flex items-center"
        >
          ← Back to Dashboard
        </button>
        
        <h1 className="text-3xl font-bold text-white mb-2">{studySet.name}</h1>
        
        <div className="flex items-center space-x-4 text-sm text-gray-400">
          <span className="capitalize">{studySet.type}</span>
          {studySet.is_ai_generated && (
            <span className="bg-primary-500/20 text-primary-400 px-2 py-1 rounded">
              AI Generated
            </span>
          )}
          <span>
            Created {new Date(studySet.created_at).toLocaleDateString()}
          </span>
        </div>
      </div>

      {/* Study Mode Selection */}
      <div className="bg-background-secondary rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold text-white mb-4">Choose Study Mode</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {/* Flashcard Mode */}
          {hasFlashcards && (
            <div
              className={`
                p-4 rounded-lg border-2 cursor-pointer transition-all
                ${selectedMode === 'flashcards'
                  ? 'border-primary-500 bg-primary-500/10'
                  : 'border-gray-600 hover:border-gray-500'
                }
              `}
              onClick={() => setSelectedMode('flashcards')}
            >
              <div className="flex items-center space-x-3">
                <div className="text-2xl">🃏</div>
                <div>
                  <h3 className="font-medium text-white">Flashcard Review</h3>
                  <p className="text-sm text-gray-400">
                    {flashcards?.length} flashcards • Interactive review
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Quiz Mode */}
          {hasQuestions && (
            <div
              className={`
                p-4 rounded-lg border-2 cursor-pointer transition-all
                ${selectedMode === 'quiz'
                  ? 'border-primary-500 bg-primary-500/10'
                  : 'border-gray-600 hover:border-gray-500'
                }
              `}
              onClick={() => setSelectedMode('quiz')}
            >
              <div className="flex items-center space-x-3">
                <div className="text-2xl">📝</div>
                <div>
                  <h3 className="font-medium text-white">Quiz Practice</h3>
                  <p className="text-sm text-gray-400">
                    {questions?.length} questions • Test your knowledge
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Start Button */}
        <Button
          onClick={handleStartStudy}
          disabled={!selectedMode}
          className="w-full"
          size="lg"
        >
          {selectedMode 
            ? `Start ${selectedMode === 'flashcards' ? 'Flashcard Review' : 'Quiz Practice'}`
            : 'Select a study mode'
          }
        </Button>
      </div>

      {/* Study Set Info */}
      <div className="bg-background-secondary rounded-lg p-6">
        <h3 className="text-lg font-medium text-white mb-4">Study Set Details</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-300 mb-2">Content</h4>
            <div className="space-y-1 text-sm text-gray-400">
              {hasFlashcards && (
                <div>{flashcards?.length} flashcards</div>
              )}
              {hasQuestions && (
                <div>{questions?.length} quiz questions</div>
              )}
            </div>
          </div>

          {studySet.source_documents && studySet.source_documents.length > 0 && (
            <div>
              <h4 className="text-sm font-medium text-gray-300 mb-2">Source Documents</h4>
              <div className="space-y-1 text-sm text-gray-400">
                {studySet.source_documents.map((doc, index) => (
                  <div key={index}>{doc.filename}</div>
                ))}
              </div>
            </div>
          )}

          {studySet.custom_prompt && (
            <div className="md:col-span-2">
              <h4 className="text-sm font-medium text-gray-300 mb-2">Custom Instructions</h4>
              <p className="text-sm text-gray-400">{studySet.custom_prompt}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
