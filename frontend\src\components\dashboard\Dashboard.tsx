import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { StudySet } from "../../../../shared/types";
import { Button } from "../common/Button";

export const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [studySets, setStudySets] = useState<StudySet[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchStudySets();
  }, []);

  const fetchStudySets = async () => {
    try {
      const token = localStorage.getItem('auth_token');
      const response = await fetch('/api/study-sets', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch study sets');
      }

      const result = await response.json();
      if (result.success) {
        setStudySets(result.data);
      } else {
        throw new Error(result.error || 'Failed to fetch study sets');
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="min-h-screen bg-background-primary text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-white">Welcome to ChewyAI Dashboard</h1>
            <p className="text-gray-400 mt-2">Manage your study materials and track your progress</p>
          </div>

          <div className="flex space-x-4">
            <Button
              onClick={() => navigate('/generate/flashcards')}
              variant="primary"
            >
              Generate Flashcards
            </Button>
            <Button
              onClick={() => navigate('/generate/quiz')}
              variant="primary"
            >
              Generate Quiz
            </Button>
          </div>
        </div>

        {/* Study Sets Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-white mb-4">Your Study Sets</h2>

          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
              <span className="ml-3 text-gray-400">Loading study sets...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="text-red-400 mb-4">Error: {error}</div>
              <Button onClick={fetchStudySets} variant="secondary">
                Try Again
              </Button>
            </div>
          ) : studySets.length === 0 ? (
            <div className="text-center py-12 bg-gray-800/50 rounded-lg">
              <div className="text-gray-400 mb-4">No study sets found</div>
              <p className="text-gray-500 mb-6">Create your first study set by generating flashcards or quizzes from your documents</p>
              <div className="flex justify-center space-x-4">
                <Button
                  onClick={() => navigate('/generate/flashcards')}
                  variant="primary"
                >
                  Generate Flashcards
                </Button>
                <Button
                  onClick={() => navigate('/generate/quiz')}
                  variant="secondary"
                >
                  Generate Quiz
                </Button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {studySets.map((studySet) => (
                <div
                  key={studySet.id}
                  className="bg-gray-800/50 rounded-lg p-6 hover:bg-gray-800/70 transition-colors cursor-pointer"
                  onClick={() => navigate(`/study-sets/${studySet.id}`)}
                >
                  <div className="flex items-start justify-between mb-4">
                    <h3 className="text-lg font-medium text-white truncate">
                      {studySet.name}
                    </h3>
                    <span className="text-xs bg-primary-500/20 text-primary-400 px-2 py-1 rounded">
                      {studySet.type}
                    </span>
                  </div>

                  <div className="space-y-2 text-sm text-gray-400">
                    <div className="flex items-center justify-between">
                      <span>Items:</span>
                      <span>{studySet.type === 'flashcards' ? studySet.flashcard_count || 0 : studySet.quiz_question_count || 0}</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span>Created:</span>
                      <span>{formatDate(studySet.created_at)}</span>
                    </div>

                    {studySet.last_studied_at && (
                      <div className="flex items-center justify-between">
                        <span>Last studied:</span>
                        <span>{formatDate(studySet.last_studied_at)}</span>
                      </div>
                    )}

                    {studySet.is_ai_generated && (
                      <div className="flex items-center space-x-1">
                        <span className="text-xs">🤖</span>
                        <span>AI Generated</span>
                      </div>
                    )}
                  </div>

                  <div className="mt-4 pt-4 border-t border-gray-700">
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        navigate(`/study-sets/${studySet.id}`);
                      }}
                    >
                      <Button
                        variant="secondary"
                        size="sm"
                        className="w-full"
                      >
                        Start Studying
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div
            className="bg-gray-800/50 rounded-lg p-6 hover:bg-gray-800/70 transition-colors cursor-pointer"
            onClick={() => navigate('/documents')}
          >
            <div className="text-2xl mb-3">📄</div>
            <h3 className="text-lg font-medium text-white mb-2">Manage Documents</h3>
            <p className="text-gray-400 text-sm">Upload and organize your study materials</p>
          </div>

          <div
            className="bg-gray-800/50 rounded-lg p-6 hover:bg-gray-800/70 transition-colors cursor-pointer"
            onClick={() => navigate('/generate/flashcards')}
          >
            <div className="text-2xl mb-3">🃏</div>
            <h3 className="text-lg font-medium text-white mb-2">Create Flashcards</h3>
            <p className="text-gray-400 text-sm">Generate flashcards from your documents</p>
          </div>

          <div
            className="bg-gray-800/50 rounded-lg p-6 hover:bg-gray-800/70 transition-colors cursor-pointer"
            onClick={() => navigate('/generate/quiz')}
          >
            <div className="text-2xl mb-3">❓</div>
            <h3 className="text-lg font-medium text-white mb-2">Create Quiz</h3>
            <p className="text-gray-400 text-sm">Generate quiz questions from your documents</p>
          </div>
        </div>
      </div>
    </div>
  );
};
