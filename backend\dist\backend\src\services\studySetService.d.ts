import { StudySet, Flashcard, QuizQuestion, StudySetType } from '../../../shared/types';
export declare class StudySetService {
    createStudySet(data: {
        user_id: string;
        name: string;
        type: StudySetType;
        is_ai_generated: boolean;
        source_documents?: {
            id: string;
            filename: string;
        }[];
        custom_prompt?: string;
    }): Promise<StudySet>;
    addFlashcardsToSet(studySetId: string, flashcards: Omit<Flashcard, 'id' | 'study_set_id' | 'is_flagged' | 'created_at' | 'updated_at'>[]): Promise<void>;
    addQuizQuestionsToSet(studySetId: string, questions: Omit<QuizQuestion, 'id' | 'study_set_id' | 'created_at' | 'updated_at'>[]): Promise<void>;
    getUserStudySets(userId: string, limit?: number, offset?: number): Promise<StudySet[]>;
    getStudySetById(studySetId: string, userId: string): Promise<StudySet | null>;
    deleteStudySet(studySetId: string, userId: string): Promise<void>;
    updateStudySet(studySetId: string, userId: string, updates: Partial<StudySet>): Promise<StudySet>;
    getFlashcardsByStudySet(studySetId: string, userId: string): Promise<any[]>;
    getQuizQuestionsByStudySet(studySetId: string, userId: string): Promise<any[]>;
}
export declare const studySetService: StudySetService;
//# sourceMappingURL=studySetService.d.ts.map