"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const supabaseService_1 = require("../services/supabaseService");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
router.post("/signup", async (req, res) => {
    try {
        const { email, password, name } = req.body;
        if (!email || !password) {
            return res
                .status(400)
                .json({ success: false, error: "Email and password are required" });
        }
        const { data: authData, error: authError } = await supabaseService_1.supabase.auth.signUp({
            email,
            password,
            options: { data: { name } },
        });
        if (authError) {
            return res.status(400).json({ success: false, error: authError.message });
        }
        if (!authData.user) {
            return res
                .status(400)
                .json({ success: false, error: "Failed to create user account" });
        }
        const profile = await supabaseService_1.supabaseService.createUserProfile(authData.user.id, email, name);
        const result = {
            success: true,
            user: profile,
            token: authData.session?.access_token,
        };
        res.status(201).json(result);
    }
    catch (error) {
        console.error('Signup error:', error);
        res.status(500).json({ success: false, error: "Internal server error" });
    }
});
router.post("/login", async (req, res) => {
    try {
        const { email, password } = req.body;
        if (!email || !password) {
            return res
                .status(400)
                .json({ success: false, error: "Email and password are required" });
        }
        const { data: authData, error: authError } = await supabaseService_1.supabase.auth.signInWithPassword({
            email,
            password,
        });
        if (authError) {
            return res.status(401).json({ success: false, error: authError.message });
        }
        if (!authData.user || !authData.session) {
            return res
                .status(401)
                .json({ success: false, error: "Authentication failed" });
        }
        const profile = await supabaseService_1.supabaseService.getUserProfile(authData.user.id);
        if (!profile) {
            return res
                .status(404)
                .json({ success: false, error: "User profile not found" });
        }
        await supabaseService_1.supabaseService.updateUserProfile(authData.user.id, {
            last_login: new Date().toISOString(),
        });
        const result = {
            success: true,
            user: profile,
            token: authData.session.access_token,
        };
        res.json(result);
    }
    catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ success: false, error: "Internal server error" });
    }
});
router.get("/user", auth_1.authenticateToken, async (req, res) => {
    try {
        const profile = await supabaseService_1.supabaseService.getUserProfile(req.user.id);
        res.json({ success: true, data: profile });
    }
    catch (error) {
        console.error('Get user error:', error);
        res
            .status(500)
            .json({ success: false, error: "Failed to get user profile" });
    }
});
router.post("/logout", auth_1.authenticateToken, async (_req, res) => {
    try {
        // Supabase handles token invalidation on client side
        res.json({ success: true, message: "Logged out successfully" });
    }
    catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({
            success: false,
            error: 'Logout failed'
        });
    }
});
exports.default = router;
//# sourceMappingURL=auth.js.map