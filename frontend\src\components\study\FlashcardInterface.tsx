import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useStudyStore } from '../../stores/studyStore';
import { Button } from '../common/Button';

export const FlashcardInterface: React.FC = () => {
  const navigate = useNavigate();
  const {
    currentSession,
    studySetContent,
    nextItem,
    previousItem,
    toggleFlag,
    markReviewed,
    endStudySession,
    updateTimeSpent
  } = useStudyStore();

  const [isFlipped, setIsFlipped] = useState(false);
  const [startTime, setStartTime] = useState(Date.now());

  useEffect(() => {
    const interval = setInterval(() => {
      updateTimeSpent(1);
    }, 1000);

    return () => clearInterval(interval);
  }, [updateTimeSpent]);

  useEffect(() => {
    // Reset flip state when moving to new card
    setIsFlipped(false);
    setStartTime(Date.now());
  }, [currentSession?.currentIndex]);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') {
        handlePrevious();
      } else if (e.key === 'ArrowRight') {
        handleNext();
      } else if (e.key === ' ') {
        e.preventDefault();
        setIsFlipped(!isFlipped);
      } else if (e.key === 'f') {
        handleToggleFlag();
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [isFlipped]);

  if (!currentSession || !studySetContent?.flashcards) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">No flashcard session found</div>
          <Button onClick={() => navigate('/dashboard')} variant="secondary">
            Back to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  const flashcards = studySetContent.flashcards;
  const currentFlashcard = flashcards[currentSession.currentIndex];
  const progress = ((currentSession.currentIndex + 1) / currentSession.totalItems) * 100;
  const isFirstCard = currentSession.currentIndex === 0;
  const isLastCard = currentSession.currentIndex === currentSession.totalItems - 1;
  const isFlagged = currentSession.flaggedItems.includes(currentFlashcard.id);

  const handleNext = () => {
    if (isFlipped) {
      markReviewed(currentFlashcard.id);
    }
    
    if (isLastCard) {
      handleFinishStudy();
    } else {
      nextItem();
    }
  };

  const handlePrevious = () => {
    if (!isFirstCard) {
      previousItem();
    }
  };

  const handleToggleFlag = () => {
    toggleFlag(currentFlashcard.id);
  };

  const handleFinishStudy = () => {
    const timeSpent = Math.floor((Date.now() - startTime) / 1000);
    const reviewedCount = currentSession.reviewedItems.length;
    const flaggedCount = currentSession.flaggedItems.length;

    endStudySession();
    
    // Show completion modal or navigate with results
    alert(`Study session complete!\n\nReviewed: ${reviewedCount}/${currentSession.totalItems} cards\nFlagged: ${flaggedCount} cards\nTime spent: ${Math.floor(timeSpent / 60)}m ${timeSpent % 60}s`);
    
    navigate(`/study-sets/${currentSession.studySetId}`);
  };

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <button
          onClick={() => navigate(`/study-sets/${currentSession.studySetId}`)}
          className="text-gray-400 hover:text-white flex items-center"
        >
          ← Back to Study Set
        </button>
        
        <div className="text-center">
          <h1 className="text-xl font-semibold text-white">
            {studySetContent.studySet?.name}
          </h1>
          <p className="text-sm text-gray-400">
            Card {currentSession.currentIndex + 1} of {currentSession.totalItems}
          </p>
        </div>

        <Button
          onClick={handleFinishStudy}
          variant="secondary"
          size="sm"
        >
          Finish
        </Button>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="w-full bg-gray-700 rounded-full h-2">
          <div 
            className="bg-primary-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          ></div>
        </div>
        <div className="flex justify-between text-xs text-gray-400 mt-1">
          <span>Progress: {Math.round(progress)}%</span>
          <span>Time: {Math.floor(currentSession.timeSpent / 60)}:{(currentSession.timeSpent % 60).toString().padStart(2, '0')}</span>
        </div>
      </div>

      {/* Flashcard */}
      <div className="mb-8 flashcard-container">
        <div 
          className={`
            relative w-full h-96 cursor-pointer transition-transform duration-500 transform-style-preserve-3d
            ${isFlipped ? 'rotate-y-180' : ''}
          `}
          onClick={() => setIsFlipped(!isFlipped)}
        >
          {/* Front of card */}
          <div className={`
            absolute inset-0 w-full h-full backface-hidden
            bg-background-secondary border border-gray-600 rounded-lg p-8
            flex items-center justify-center text-center
            ${isFlipped ? 'rotate-y-180' : ''}
          `}>
            <div>
              <div className="text-sm text-gray-400 mb-4">FRONT</div>
              <div className="text-xl text-white leading-relaxed">
                {currentFlashcard.front}
              </div>
              {!isFlipped && (
                <div className="text-sm text-gray-500 mt-6">
                  Click to reveal answer
                </div>
              )}
            </div>
          </div>

          {/* Back of card */}
          <div className={`
            absolute inset-0 w-full h-full backface-hidden rotate-y-180
            bg-primary-500/10 border border-primary-500/30 rounded-lg p-8
            flex items-center justify-center text-center
            ${isFlipped ? '' : 'rotate-y-180'}
          `}>
            <div>
              <div className="text-sm text-primary-400 mb-4">BACK</div>
              <div className="text-xl text-white leading-relaxed">
                {currentFlashcard.back}
              </div>
              {isFlipped && (
                <div className="text-sm text-gray-500 mt-6">
                  Click to flip back
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between">
        <Button
          onClick={handlePrevious}
          disabled={isFirstCard}
          variant="secondary"
        >
          ← Previous
        </Button>

        <div className="flex items-center space-x-4">
          <Button
            onClick={handleToggleFlag}
            variant={isFlagged ? "primary" : "secondary"}
            size="sm"
          >
            {isFlagged ? '🚩 Flagged' : '🏳️ Flag'}
          </Button>

          <Button
            onClick={() => setIsFlipped(!isFlipped)}
            variant="secondary"
          >
            {isFlipped ? 'Show Front' : 'Show Back'}
          </Button>
        </div>

        <Button
          onClick={handleNext}
          variant="primary"
        >
          {isLastCard ? 'Finish' : 'Next →'}
        </Button>
      </div>

      {/* Keyboard shortcuts */}
      <div className="mt-8 text-center text-sm text-gray-500">
        <p>Keyboard shortcuts: ← → (navigate) • Space (flip) • F (flag)</p>
      </div>
    </div>
  );
};
